eureka.client.enabled=false
spring.shardingsphere.datasource.names=master,read
spring.shardingsphere.sharding.default-data-source-name=master
mybatis.configuration.log-impl= org.apache.ibatis.logging.stdout.StdOutImpl

gvcore.report = backend
report.consumption.enable = false
Ddruid.mysql.usePingMethod=false

spring.shardingsphere.datasource.master.type=com.alibaba.druid.pool.DruidDataSource
spring.shardingsphere.datasource.master.driver-class-name=com.mysql.cj.jdbc.Driver

spring.shardingsphere.datasource.master.url=**********************************************************************************************************************
spring.shardingsphere.datasource.master.username=gtech-dev
spring.shardingsphere.datasource.master.password=gtech-dev
spring.shardingsphere.datasource.master.max-active=300
spring.shardingsphere.datasource.master.initial-size=50
spring.shardingsphere.datasource.master.min-idle=50
spring.shardingsphere.datasource.master.max-wait=60000
spring.shardingsphere.datasource.master.pool-prepared-statements=true
spring.shardingsphere.datasource.master.max-pool-prepared-statement-per-connection-size=20
spring.shardingsphere.datasource.master.validation-query=SELECT 1 FROM DUAL
spring.shardingsphere.datasource.master.test-on-borrow=false
spring.shardingsphere.datasource.master.test-on-return=false
spring.shardingsphere.datasource.master.test-while-idle=true
spring.shardingsphere.datasource.master.time-between-eviction-runs-millis=60000
spring.shardingsphere.datasource.master.min-evictable-idle-time-millis=300000
spring.shardingsphere.datasource.master.connection-properties=druid.stat.mergeSql=true;druid.stat.slowSqlMillis=5000

spring.shardingsphere.datasource.read.type=com.alibaba.druid.pool.DruidDataSource
spring.shardingsphere.datasource.read.driver-class-name=com.mysql.cj.jdbc.Driver

spring.shardingsphere.datasource.read.url=**********************************************************************************************************************
spring.shardingsphere.datasource.read.username=gtech-dev
spring.shardingsphere.datasource.read.password=gtech-dev
#spring.shardingsphere.datasource.read.url = *************************************************************************************************************************************************************
#spring.shardingsphere.datasource.read.username = gtechtest
#spring.shardingsphere.datasource.read.password = heT5MDxtk5PxIrYj
#spring.shardingsphere.datasource.read.url=*************************************************************************************************************************************************************************
#spring.shardingsphere.datasource.read.username=gvreadonly
#spring.shardingsphere.datasource.read.password=bfV5WWmCA

spring.shardingsphere.datasource.read.max-active=300
spring.shardingsphere.datasource.read.initial-size=50
spring.shardingsphere.datasource.read.min-idle=50
spring.shardingsphere.datasource.read.max-wait=60000
spring.shardingsphere.datasource.read.pool-prepared-statements=true
spring.shardingsphere.datasource.read.max-pool-prepared-statement-per-connection-size=20
spring.shardingsphere.datasource.read.validation-query=SELECT 1 FROM DUAL
spring.shardingsphere.datasource.read.test-on-borrow=false
spring.shardingsphere.datasource.read.test-on-return=false
spring.shardingsphere.datasource.read.test-while-idle=true
spring.shardingsphere.datasource.read.time-between-eviction-runs-millis=60000
spring.shardingsphere.datasource.read.min-evictable-idle-time-millis=300000
spring.shardingsphere.datasource.read.connection-properties=druid.stat.mergeSql=true;druid.stat.slowSqlMillis=5000

spring.main.allow-bean-definition-overriding=true
#spring.shardingsphere.props.sql.show = true
spring.shardingsphere.props.max.connections.size.per.query = 100



#gv_voucher \uFFFD\u05B1\uFFFD\uFFFD\uFFFD\uFFFD\uFFFD

spring.shardingsphere.sharding.tables.gv_voucher.actual-data-nodes=ds.gv_voucher_$->{0..63}
spring.shardingsphere.sharding.tables.gv_voucher.table-strategy.standard.sharding-column=voucher_code
spring.shardingsphere.sharding.tables.gv_voucher.table-strategy.standard.precise-algorithm-class-name=com.gtech.gvcore.common.config.VoucherShardingConfig


spring.shardingsphere.sharding.tables.gv_transaction_data.actual-data-nodes=ds.gv_transaction_data_$->{0..63}
spring.shardingsphere.sharding.tables.gv_transaction_data.table-strategy.standard.sharding-column=voucher_code
spring.shardingsphere.sharding.tables.gv_transaction_data.table-strategy.standard.precise-algorithm-class-name=com.gtech.gvcore.common.config.TransactionDataShardingConfig

spring.shardingsphere.sharding.tables.gv_voucher_log.actual-data-nodes=ds.gv_voucher_log_$->{0..63}
spring.shardingsphere.sharding.tables.gv_voucher_log.table-strategy.standard.sharding-column=voucher_code
spring.shardingsphere.sharding.tables.gv_voucher_log.table-strategy.standard.precise-algorithm-class-name=com.gtech.gvcore.common.config.VoucherLogShardingConfig

#voucher\u8303\u56F4\u67E5\u8BE2
spring.shardingsphere.sharding.tables.gv_voucher.table-strategy.standard.range-algorithm-class-name=com.gtech.gvcore.common.config.VoucherRangeShardingConfig
spring.shardingsphere.sharding.tables.gv_transaction_data.table-strategy.standard.range-algorithm-class-name=com.gtech.gvcore.common.config.VoucherRangeShardingConfig
spring.shardingsphere.sharding.tables.gv_voucher_log.table-strategy.standard.range-algorithm-class-name=com.gtech.gvcore.common.config.VoucherRangeShardingConfig

sharding.jdbc.config.sharding.binding-tables=gv_voucher,gv_transaction_data,gv_voucher_log



eureka.client.serviceUrl.defaultZone = http://eureka-dev.gtech.asia/eureka/
eureka.instance.lease-expiration-duration-in-seconds = 20
eureka.instance.lease-renewal-interval-in-seconds = 10

spring.jackson.time-zone = GMT+8
spring.jackson.mapper.accept-case-insensitive-properties=true


gv.sftp.url = ftp://47.88.236.77:21211/upload/Test
gv.sftp.name = titan_sta_map
gv.sftp.pwd = 123titan_sta_map

logging.pattern.level = trace_id=%mdc{trace_id} span_id=%mdc{span_id} trace_flags=%mdc{trace_flags} %5p
logging.pattern.console=%black(%d{yyyy-MM-dd HH:mm:ss.SSS}) %highlight(%-5level) %yellow(%C:%L{1.}) %msg%n%throwable
logging.level.org.springframework.transaction=DEBUG

# Report version check configuration
# Set to false to disable git version check for report queue isolation
# Default: true (enable version check for safety)
report.version.check.enable=true

# REDIS (CasabaRedisProperties)
# Redis\u6570\u636E\u5E93\u7D22\u5F15\uFF08\u9ED8\u8BA4\u4E3A0\uFF09
spring.redis.database=5
# Redis\u670D\u52A1\u5668\u5730\u5740
spring.redis.host=*************
# Redis\u670D\u52A1\u5668\u8FDE\u63A5\u7AEF\u53E3
spring.redis.port=6379
# Redis\u670D\u52A1\u5668\u8FDE\u63A5\u5BC6\u7801\uFF08\u9ED8\u8BA4\u4E3A\u7A7A\uFF09
#spring.redis.password=
# \u8FDE\u63A5\u8D85\u65F6\u65F6\u95F4\uFF08\u6BEB\u79D2\uFF09
#spring.redis.timeout=0

# \u8FDE\u63A5\u6C60\u6700\u5927\u8FDE\u63A5\u6570\uFF08\u4F7F\u7528\u8D1F\u503C\u8868\u793A\u6CA1\u6709\u9650\u5236\uFF09
spring.redis.lettuce.pool.max-active=8
# \u8FDE\u63A5\u6C60\u6700\u5927\u963B\u585E\u7B49\u5F85\u65F6\u95F4\uFF08\u4F7F\u7528\u8D1F\u503C\u8868\u793A\u6CA1\u6709\u9650\u5236\uFF09
spring.redis.lettuce.pool.max-wait=-1
# \u8FDE\u63A5\u6C60\u4E2D\u7684\u6700\u5927\u7A7A\u95F2\u8FDE\u63A5
spring.redis.lettuce.pool.max-idle=8
# \u8FDE\u63A5\u6C60\u4E2D\u7684\u6700\u5C0F\u7A7A\u95F2\u8FDE\u63A5
spring.redis.lettuce.pool.min-idle=0

gtech.titan.idm.templateExpression.applicationCode=APP[SA:%03d]
gtech.titan.idm.templateExpression.domainCode=10[SA:%04d]
gtech.titan.idm.templateExpression.tenantCode=10[SA:%04d]
gtech.titan.idm.templateExpression.orgCode=10[SA:%06d]
gtech.titan.idm.templateExpression.userCode=10[D:yyMMdd][SA:%06d]
gtech.titan.idm.templateExpression.roleCode=RO[SA:%06d]
gtech.titan.idm.templateExpression.resourceCode=RE[SA:%06d]
gtech.titan.idm.templateExpression.menuCode=ME[SA:%06d]
gtech.titan.idm.templateExpression.uniqueCode=UN[SA:%06d]
gtech.titan.store.templateExpression.storeCode=10[D:yyMMDDHH][SA:%02d]

titan.gateway.url=http://gateway-dev.gtech.asia/api
titan.gv.url=http://gv-dev.gtech.asia


# thread pool
thread.corePoolSize = 10
thread.maximumPoolSize = 50
thread.queueSize = 1000



rocketmq.name-server = 172.19.109.56:9876
rocketmq.producer.group = titan_gvcore
rocketmq.producer.groupname = gvcore_router_engine_producer_dev
rocketmq.producer.nameserver = 172.19.109.56:9876
rocketmq.producer.topic = gvcore_router_engine_dev
rocketmq.producer.topic.tag = event

rocketmq.consumer.group = titan_gvcore_backend
rocketmq.consumer.groupname = gvcore_router_backend_dev
rocketmq.consumer.nameserver = 172.19.109.56:9876
rocketmq.consumer.topic = backend
rocketmq.consumer.topic.tag = event
rocketmq.producer.callback.groupname = gvcore_router_engine_callback_producer
rocketmq.producer.callback.nameserver = 172.19.109.56:9876
###consumer
rocketmq.consumer.consumeThreadMin = 20
rocketmq.consumer.consumeThreadMax = 64
#\u8BBE\u7F6E\u4E00\u6B21\u6D88\u8D39\u6D88\u606F\u7684\u6761\u6570\uFF0C\u9ED8\u8BA4\u4E3A1\u6761
rocketmq.consumer.consumeMessageBatchMaxSize = 1

###producer
#\u6D88\u606F\u6700\u5927\u957F\u5EA6 \u9ED8\u8BA41024*4(4M)
rocketmq.producer.maxMessageSize = 4096000
#\u53D1\u9001\u6D88\u606F\u8D85\u65F6\u65F6\u95F4,\u9ED8\u8BA43000
rocketmq.producer.sendMsgTimeout = 3000
#\u53D1\u9001\u6D88\u606F\u5931\u8D25\u91CD\u8BD5\u6B21\u6570\uFF0C\u9ED8\u8BA42
rocketmq.producer.retryTimesWhenSendFailed = 2

titan.hub.uri = http://hub-dev.gtech.asia/api/v{version}/{apiName}
titan.hub.appid=STORE
titan.hub.appsecret=U1RPUkUtTUFOQUdFUg==

management.health.db.enabled=false
management.health.ldap.enabled=false

# FileCloud
titan.callback.import-file-callback=https://gateway-dev.gtech.asia/api/gvcore/filecloud/import/callback?value={0}
titan.filecloud.context-path=/filecloud
spring.data.mongodb.host = *************
spring.data.mongodb.username = filecloud
spring.data.mongodb.password = filecloud
spring.data.mongodb.database = dev_filecloud_mongo

task-admin.userName= admin
task-admin.password= tcz2flw!

filecloud.oss.default-config.access-key-id = LTAI5tKVq2przaji8mR7g8DK
filecloud.oss.default-config.access-key-secret = ******************************
filecloud.oss.default-config.endpoint = oss-cn-shanghai.aliyuncs.com
filecloud.oss.default-config.public-bucket.name = gtech
filecloud.oss.default-config.public-bucket.domain = https://static.gtech.asia
filecloud.oss.default-config.private-bucket.name = gtech-biz
filecloud.oss.default-config.private-bucket.domain = http://file.gtech.asia
filecloud.oss.default-config.private-bucket.expire-time = 2M
filecloud.oss.default-config.public-bucket.expire-time = 1D
filecloud.oss.default-config.endpoint-scheme = https



###########################################MESSAGE-START#######################################
domain.code.default = DEFAULT
tenant.code.default = DEFAULT
org.code.default = DEFAULT

send.mode.supplier-code.twilio = SC001
send.mode.supplier-code.aliyun = SC011
send.mode.supplier-code.dahantc = SC021
send.mode.supplier-code.sprint = SC031
send.mode.supplier-code.jartis = SC041
send.mode.supplier-code.sandeza = SC051
send.mode.supplier-code.isms = SC061
send.mode.supplier-code.vonage = SC071

send.mode.supplier-code.sendgrid = SC002
send.mode.supplier-code.mailchimp = SC012
send.mode.supplier-code.aliyun.smtp = SC032

send.mode.supplier-code.jpush = SC003
send.mode.supplier-code.firebase = SC013

message.rocketmq.producer.groupname = gvcore_message_engine_producer_dev
message.rocketmq.producer.nameserver = 172.19.109.56:9876
message.rocketmq.producer.topic = gvcore_message_engine_dev
message.rocketmq.producer.topic.tag = event
message.rocketmq.consumer.groupname = gvcore_message_engine_cosumer_dev
message.rocketmq.consumer.nameserver = 172.19.109.56:9876
message.rocketmq.consumer.topic = gvcore_message_engine_dev
message.rocketmq.consumer.topic.tag = event
message.rocketmq.producer.callback.groupname = gvcore_message_engine_callback_producer
message.rocketmq.producer.callback.nameserver = 172.19.109.56:9876
template.mode.code.default = MC0016
template.mode.type.default = 0

manual.event.code.default = { 0 : 'EVM001',1 : 'EVM001',2 : 'EVM001',3 : 'EVM001',4 : 'EVM001',5 : 'EVM001'}
manual.mode.code.default = { 0 : 'MC0016',1 : 'MC0002',2 : 'MC0022',3 : 'MC0004',4 : 'MC0019',5 : 'MC0020'}


event.app.code.search.default = titan
###########################################MESSAGE-END#######################################

gv.issuer.warehouse = {MAP:'OU102205191658000005',IS102205191134000002:'OU102205191406000001',IS102205200942000003:'OU102205201807000009','IS102206161509000006':'OU102206161627000018','IS102208111009000011':'OU10230213103651000052'}
gv.issuer.businesswarehouse = {MAP:'OU102205191658000006',IS102205191134000002:'OU102205191406000002',IS102205200942000003:'OU102205201808000010','IS102206161509000006':'OU102206161630000019','IS102208111009000011':'OU10230213101607000050'}

gv.outlet.warehouse.MV01 = OU10240528141811000013
gv.outlet.warehouse.MV03 = OU10240528143201000015
gv.outlet.warehouse.MV04 = OU10240723094409000081


gv.task.base.url=https://task-dev.gtech.asia

gv.issuehandling.uploadfile.header.field={'Voucher Number':'voucherCode','Original Invoice Number':'invoiceNo','Invoice Number':'invoiceNo', 'Original Approval Code':'approvalCode', 'Original Voucher Number': 'voucherCode', 'New Voucher Number': 'newVoucherCode', 'Email': 'receiverEmail', 'Reissuing Reason': 'remarks', 'Change Expiry Date': 'voucherEffectiveDate', 'Merchant Outlet Name': 'outletName', 'OldActivationCode': 'oldActivationCode', 'VoucherNumber': 'voucherCode', 'CustomerEmail': 'receiverEmail','Card Number':'voucherCode','Amount': 'amount'}
gv.issuehandling.uploadfile.header.field.date.pattern={'voucherEffectiveDate': 'yyyy/M/d H:m'}

#\u6587\u4EF6\u5BFC\u51FA\u76F8\u5173
gv.export.pdfFile=/
gv.export.excelFile=/

#account\u914D\u7F6E
account.security.lockTime = 30
account.security.effectiveTime = 36500
account.security.errorCount = 5
account.security.historyCount = 5


#opUserAccount
idm.op.version=1
default.password.verification.rule=2
tenant.password.change.interval=1:90,999:36500

gv.sourceid=Mercury Test Source
gv.messageid=Mercury Test Message


gv.active.url=https://staging.mapclub.com/en/e-gv?code=
qc.extend.url=http://gv-dev.gtech.asia/gvcore

gvcore.jwt.effective.hours = 5
#report.consumption.enable = false

# Swagger配置
springfox.documentation.enabled=true
springfox.documentation.swagger.v2.path=/api/gvcore/v2/api-docs